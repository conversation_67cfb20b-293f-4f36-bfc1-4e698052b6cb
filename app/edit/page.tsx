import { WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import EditCanvas from "./canvas.client";

export const metadata: Metadata = {
	title: `AI Image Editor | ${WEBNAME}`,
	description: "",
	alternates: {
		canonical: "/edit",
	},
	robots: {
		index: false,
		follow: false,
	},
};

export default async function Page() {
	return (
		<div className="h-[calc(100vh-64px)] overflow-hidden border-t">
			<EditCanvas />
		</div>
	);
}
